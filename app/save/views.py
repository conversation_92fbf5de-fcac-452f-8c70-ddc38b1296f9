#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
存档系统视图
"""

from flask import Blueprint, jsonify, request, render_template
from app.models import Hotel, Employee, Department, Room, MarketingCampaign, FinancialRecord, Achievement, db
from app.main.utils import get_current_hotel
import json
import logging
from datetime import datetime, date
import os

logger = logging.getLogger(__name__)

bp = Blueprint('save', __name__, url_prefix='/save')

@bp.route('/create', methods=['POST'])
def create_save():
    """创建存档"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        save_name = data.get('save_name', f"存档_{datetime.now().strftime('%Y%m%d_%H%M%S')}")

        # 收集所有游戏数据
        save_data = {
            'save_name': save_name,
            'created_at': datetime.now().isoformat(),
            'hotel': {
                'name': hotel.name,
                'level': hotel.level,
                'money': hotel.money,
                'reputation': hotel.reputation,
                'date': hotel.date.isoformat() if hotel.date else None,
                'start_date': hotel.start_date.isoformat() if hotel.start_date else None
            },
            'employees': [],
            'departments': [],
            'rooms': [],
            'marketing_campaigns': [],
            'financial_records': [],
            'achievements': []
        }

        # 收集员工数据
        employees = Employee.query.filter_by(hotel_id=hotel.id).all()
        for emp in employees:
            save_data['employees'].append({
                'name': emp.name,
                'department': emp.department,
                'level': emp.level,
                'salary': emp.salary,
                'years_worked': emp.years_worked,
                'hire_date': emp.hire_date.isoformat() if emp.hire_date else None
            })

        # 收集部门数据
        departments = Department.query.filter_by(hotel_id=hotel.id).all()
        for dept in departments:
            save_data['departments'].append({
                'name': dept.name,
                'is_unlocked': dept.is_unlocked
            })

        # 收集房间数据
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        for room in rooms:
            save_data['rooms'].append({
                'type': room.type,
                'count': room.count
            })

        # 收集营销活动数据
        campaigns = MarketingCampaign.query.filter_by(hotel_id=hotel.id).all()
        for campaign in campaigns:
            save_data['marketing_campaigns'].append({
                'campaign_id': campaign.campaign_id,
                'is_active': campaign.is_active,
                'start_date': campaign.start_date.isoformat() if campaign.start_date else None,
                'end_date': campaign.end_date.isoformat() if campaign.end_date else None
            })

        # 收集财务记录（最近100条）
        records = FinancialRecord.query.filter_by(hotel_id=hotel.id).order_by(FinancialRecord.record_date.desc()).limit(100).all()
        for record in records:
            save_data['financial_records'].append({
                'record_date': record.record_date.isoformat() if record.record_date else None,
                'description': record.description,
                'income': record.income,
                'expense': record.expense
            })

        # 收集成就数据
        achievements = Achievement.query.filter_by(hotel_id=hotel.id).all()
        for achievement in achievements:
            save_data['achievements'].append({
                'name': achievement.name,
                'description': achievement.description,
                'category': achievement.category,
                'achieved': achievement.achieved,
                'achieved_date': achievement.achieved_date.isoformat() if achievement.achieved_date else None,
                'reward_claimed': getattr(achievement, 'reward_claimed', False),
                'reward_money': getattr(achievement, 'reward_money', 0),
                'reward_reputation': achievement.reward_reputation
            })

        # 保存到文件
        save_dir = 'saves'
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        save_file = os.path.join(save_dir, f"{save_name}.json")
        with open(save_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        return jsonify({
            "success": True,
            "message": f"存档'{save_name}'创建成功",
            "save_file": save_file
        })

    except Exception as e:
        logger.error(f"创建存档时出错: {e}")
        return jsonify({"success": False, "message": "创建存档失败"}), 500

@bp.route('/list', methods=['GET'])
def list_saves():
    """获取存档列表"""
    try:
        save_dir = 'saves'
        if not os.path.exists(save_dir):
            return jsonify({"success": True, "saves": []})

        saves = []
        for filename in os.listdir(save_dir):
            if filename.endswith('.json'):
                save_file = os.path.join(save_dir, filename)
                try:
                    with open(save_file, 'r', encoding='utf-8') as f:
                        save_data = json.load(f)
                    
                    saves.append({
                        'filename': filename,
                        'save_name': save_data.get('save_name', filename[:-5]),
                        'created_at': save_data.get('created_at'),
                        'hotel_name': save_data.get('hotel', {}).get('name', '未知'),
                        'hotel_level': save_data.get('hotel', {}).get('level', 1),
                        'hotel_money': save_data.get('hotel', {}).get('money', 0)
                    })
                except:
                    continue

        saves.sort(key=lambda x: x['created_at'], reverse=True)
        return jsonify({"success": True, "saves": saves})

    except Exception as e:
        logger.error(f"获取存档列表时出错: {e}")
        return jsonify({"success": False, "message": "获取存档列表失败"}), 500

@bp.route('/load', methods=['POST'])
def load_save():
    """读取存档"""
    try:
        data = request.get_json()
        filename = data.get('filename')
        
        if not filename:
            return jsonify({"success": False, "message": "存档文件名不能为空"}), 400

        save_file = os.path.join('saves', filename)
        if not os.path.exists(save_file):
            return jsonify({"success": False, "message": "存档文件不存在"}), 404

        # 读取存档数据
        with open(save_file, 'r', encoding='utf-8') as f:
            save_data = json.load(f)

        # 清空当前数据
        hotel = get_current_hotel()
        if hotel:
            # 删除相关数据
            Employee.query.filter_by(hotel_id=hotel.id).delete()
            Department.query.filter_by(hotel_id=hotel.id).delete()
            Room.query.filter_by(hotel_id=hotel.id).delete()
            MarketingCampaign.query.filter_by(hotel_id=hotel.id).delete()
            FinancialRecord.query.filter_by(hotel_id=hotel.id).delete()
            Achievement.query.filter_by(hotel_id=hotel.id).delete()
            
            # 更新酒店数据
            hotel_data = save_data['hotel']
            hotel.name = hotel_data['name']
            hotel.level = hotel_data['level']
            hotel.money = hotel_data['money']
            hotel.reputation = hotel_data['reputation']
            hotel.date = datetime.fromisoformat(hotel_data['date']).date() if hotel_data['date'] else date.today()
            hotel.start_date = datetime.fromisoformat(hotel_data['start_date']).date() if hotel_data['start_date'] else date.today()

        else:
            # 创建新酒店
            hotel_data = save_data['hotel']
            hotel = Hotel(
                name=hotel_data['name'],
                level=hotel_data['level'],
                money=hotel_data['money'],
                reputation=hotel_data['reputation'],
                date=datetime.fromisoformat(hotel_data['date']).date() if hotel_data['date'] else date.today(),
                start_date=datetime.fromisoformat(hotel_data['start_date']).date() if hotel_data['start_date'] else date.today()
            )
            db.session.add(hotel)
            db.session.flush()  # 获取hotel.id

        # 恢复员工数据
        for emp_data in save_data['employees']:
            employee = Employee(
                hotel_id=hotel.id,
                name=emp_data['name'],
                department=emp_data['department'],
                level=emp_data['level'],
                salary=emp_data['salary'],
                years_worked=emp_data['years_worked'],
                hire_date=datetime.fromisoformat(emp_data['hire_date']).date() if emp_data['hire_date'] else date.today()
            )
            db.session.add(employee)

        # 恢复部门数据
        for dept_data in save_data['departments']:
            department = Department(
                hotel_id=hotel.id,
                name=dept_data['name'],
                is_unlocked=dept_data['is_unlocked']
            )
            db.session.add(department)

        # 恢复房间数据
        for room_data in save_data['rooms']:
            room = Room(
                hotel_id=hotel.id,
                type=room_data['type'],
                count=room_data['count']
            )
            db.session.add(room)

        # 恢复营销活动数据
        for campaign_data in save_data['marketing_campaigns']:
            campaign = MarketingCampaign(
                hotel_id=hotel.id,
                campaign_id=campaign_data['campaign_id'],
                is_active=campaign_data['is_active'],
                start_date=datetime.fromisoformat(campaign_data['start_date']).date() if campaign_data['start_date'] else None,
                end_date=datetime.fromisoformat(campaign_data['end_date']).date() if campaign_data['end_date'] else None
            )
            db.session.add(campaign)

        # 恢复财务记录
        for record_data in save_data['financial_records']:
            record = FinancialRecord(
                hotel_id=hotel.id,
                record_date=datetime.fromisoformat(record_data['record_date']).date() if record_data['record_date'] else date.today(),
                description=record_data['description'],
                income=record_data['income'],
                expense=record_data['expense']
            )
            db.session.add(record)

        # 恢复成就数据
        for achievement_data in save_data['achievements']:
            achievement = Achievement(
                hotel_id=hotel.id,
                name=achievement_data['name'],
                description=achievement_data['description'],
                category=achievement_data['category'],
                condition_type='custom',
                condition_value=0,
                reward_reputation=achievement_data['reward_reputation'],
                reward_money=achievement_data.get('reward_money', 0),
                achieved=achievement_data['achieved'],
                achieved_date=datetime.fromisoformat(achievement_data['achieved_date']).date() if achievement_data['achieved_date'] else None,
                reward_claimed=achievement_data.get('reward_claimed', False)
            )
            db.session.add(achievement)

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"存档'{save_data['save_name']}'读取成功"
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"读取存档时出错: {e}")
        return jsonify({"success": False, "message": "读取存档失败"}), 500

@bp.route('/restart', methods=['POST'])
def restart_game():
    """重新开始游戏"""
    try:
        # 清空所有数据
        hotel = get_current_hotel()
        if hotel:
            Employee.query.filter_by(hotel_id=hotel.id).delete()
            Department.query.filter_by(hotel_id=hotel.id).delete()
            Room.query.filter_by(hotel_id=hotel.id).delete()
            MarketingCampaign.query.filter_by(hotel_id=hotel.id).delete()
            FinancialRecord.query.filter_by(hotel_id=hotel.id).delete()
            Achievement.query.filter_by(hotel_id=hotel.id).delete()
            
            # 重置酒店数据
            hotel.name = "新手酒店"
            hotel.level = 1
            hotel.money = 1000000  # 100万初始资金
            hotel.reputation = 50
            hotel.date = date.today()
            hotel.start_date = date.today()
        else:
            # 创建新酒店
            hotel = Hotel(
                name="新手酒店",
                level=1,
                money=1000000,
                reputation=50,
                date=date.today(),
                start_date=date.today()
            )
            db.session.add(hotel)
            db.session.flush()

        # 初始化基础数据
        from app.main.utils import initialize_hotel_data
        initialize_hotel_data(hotel)

        db.session.commit()

        return jsonify({
            "success": True,
            "message": "游戏重新开始成功"
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"重新开始游戏时出错: {e}")
        return jsonify({"success": False, "message": "重新开始游戏失败"}), 500
