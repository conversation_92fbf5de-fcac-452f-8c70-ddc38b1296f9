from flask import render_template, jsonify, request
from app.main import bp
from app.models import Hotel, Employee, Department, Room, FinancialRecord, GameSetting
from app.models import initialize_game_data as create_initial_data  # 添加缺失的导入
from app.main.utils import (calculate_daily_finances, update_employees_work_age, 
                           promote_employees, deduct_monthly_salaries, get_current_hotel,
                           calculate_satisfaction_and_reputation, calculate_department_busy_level,
                           time_advance)
from app.main.utils import ROOM_OCCUPANCY_RANGES  # 导入房间入住率范围定义
from app import db
from datetime import datetime, timedelta
import random
import logging
import json
import os

logger = logging.getLogger(__name__)


@bp.route('/')
@bp.route('/index')
def index():
    """首页"""
    hotel = get_current_hotel()
    if not hotel:
        # 如果没有酒店数据，创建初始数据
        create_initial_data()
        hotel = get_current_hotel()
    
    # 获取部门信息
    departments = Department.query.filter_by(hotel_id=hotel.id).all()
    
    return render_template('index.html', hotel=hotel, departments=departments)


@bp.route('/api/hotel_info')
def hotel_info():
    """获取酒店信息API"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        # 获取员工总数
        employee_count = Employee.query.filter_by(hotel_id=hotel.id).count()
        
        # 获取房间总数
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        total_rooms = sum(room.count for room in rooms)
        
        # 获取客人总数（基于实际入住率计算）
        total_guests = 0
        for room in rooms:
            # 获取该房间类型的当日入住率
            occupancy_rate = calculate_daily_occupancy_rate(hotel, room.type, hotel.date)
            # 计算实际入住客人数
            guests_in_room = int(room.count * (occupancy_rate / 100))
            total_guests += guests_in_room
        
        # 计算真实的月度财务数据
        from app.models import FinancialRecord
        from datetime import datetime, timedelta

        # 获取当月的财务记录
        current_month_start = hotel.date.replace(day=1)
        next_month_start = (current_month_start + timedelta(days=32)).replace(day=1)

        monthly_records = FinancialRecord.query.filter(
            FinancialRecord.hotel_id == hotel.id,
            FinancialRecord.record_date >= current_month_start,
            FinancialRecord.record_date < next_month_start
        ).all()

        monthly_income = sum(record.income for record in monthly_records)
        monthly_expense = sum(record.expense for record in monthly_records)
        monthly_profit = monthly_income - monthly_expense

        # 如果没有财务记录，基于当前状态估算
        if not monthly_records:
            # 估算月收入（基于房间和入住率）
            daily_income = 0
            for room in rooms:
                from app.main.utils import calculate_stable_occupancy_rate
                occupancy_rate = calculate_stable_occupancy_rate(hotel, room.type, hotel.date) / 100
                daily_income += room.count * room.price * occupancy_rate

            # 估算月支出（员工工资 + 维护费）
            employees = Employee.query.filter_by(hotel_id=hotel.id).all()
            monthly_salary = sum(emp.salary for emp in employees)
            monthly_maintenance = sum(room.count for room in rooms) * 100 * 30  # 每间房每月100元

            monthly_income = daily_income * 30  # 30天估算
            monthly_expense = monthly_salary + monthly_maintenance
            monthly_profit = monthly_income - monthly_expense
        
        # 获取房间信息和入住率数据（使用真实数据，与房间管理页面一致）
        occupancy_rates = get_real_occupancy_data(hotel)
        
        room_info = {}
        for room in rooms:
            room_info[room.type] = {
                'count': room.count,
                'price': room.price
            }

        return jsonify({
            "success": True,
            "hotel_name": hotel.name,
            "level": hotel.level,
            "current_date": hotel.date.strftime('%Y-%m-%d'),
            "money": hotel.money,
            "days_elapsed": hotel.days_elapsed,
            "reputation": hotel.reputation,
            "reputation_level": hotel.reputation_level,
            "time_running": hotel.time_running,
            "satisfaction": hotel.satisfaction,  # 新增：客户满意度
            "time_speed": getattr(hotel, 'time_speed', 1),  # 新增：时间速度
            "employee_count": employee_count,
            "total_rooms": total_rooms,
            "total_guests": total_guests,
            "monthly_income": monthly_income,
            "monthly_expense": monthly_expense,
            "monthly_profit": monthly_profit,
            "occupancy_rates": occupancy_rates,
            "room_info": room_info
        })
    except Exception as e:
        logger.error(f"获取酒店信息时出错: {e}")
        return jsonify({"success": False, "message": "获取酒店信息失败"}), 500


def calculate_occupancy_rates(hotel):
    """
    计算过去10天的入住率数据
    返回格式: {
        '单人间': [
            {'date': 'YYYY-MM-DD', 'rate': 85.5},
            ...
        ],
        ...
    }
    """
    try:
        # 获取当前日期和10天前的日期
        end_date = hotel.date
        start_date = end_date - timedelta(days=9)  # 包括今天，共10天数据
        
        # 获取酒店的所有房间类型
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        room_types = [room.type for room in rooms]
        
        # 为每种房间类型生成入住率数据
        occupancy_data = {}
        
        for room_type in room_types:
            occupancy_data[room_type] = []
            
            # 为过去10天生成数据
            for i in range(10):
                date = start_date + timedelta(days=i)
                
                # 根据房间类型确定基础入住率范围
                min_rate, max_rate = ROOM_OCCUPANCY_RANGES.get(room_type, (0.5, 1.0))
                
                # 生成随机入住率（模拟真实数据）
                base_occupancy = random.uniform(min_rate, max_rate)
                
                # 应用各种影响因素
                # 客户满意度影响
                satisfaction_factor = hotel.satisfaction / 100
                base_occupancy *= (0.8 + 0.4 * satisfaction_factor)
                
                # 酒店声望影响
                reputation_factor = min(1.0, hotel.reputation_level / 10)
                base_occupancy *= (1 + 0.1 * reputation_factor)
                
                # 季节性因素
                from app.main.utils import get_current_season
                season = get_current_season(date)
                from app.models import SeasonalEffect
                seasonal_effects = SeasonalEffect.query.filter(
                    SeasonalEffect.hotel_id == hotel.id,
                    SeasonalEffect.season == season,
                    SeasonalEffect.start_date <= date,
                    SeasonalEffect.end_date >= date
                ).all()
                
                for effect in seasonal_effects:
                    base_occupancy *= (1 + effect.effect_value / 100)
                
                # 确保入住率在合理范围内
                occupancy_rate = max(0.0, min(1.0, base_occupancy))
                
                occupancy_data[room_type].append({
                    'date': date.strftime('%Y-%m-%d'),
                    'rate': round(occupancy_rate * 100, 1)
                })
        
        return occupancy_data
    except Exception as e:
        logger.error(f"计算入住率时出错: {e}")
        # 返回默认数据
        return {}


@bp.route('/api/toggle_time_speed', methods=['POST'])
def toggle_time_speed():
    """切换时间速度"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        # 切换时间速度（1倍速 <-> 2倍速）
        current_speed = getattr(hotel, 'time_speed', 1)
        new_speed = 2 if current_speed == 1 else 1
        hotel.time_speed = new_speed

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"时间速度已切换为{new_speed}倍速",
            "time_speed": new_speed
        })
    except Exception as e:
        logger.error(f"切换时间速度时出错: {e}")
        return jsonify({"success": False, "message": "切换时间速度失败"}), 500


@bp.route('/api/toggle_time', methods=['POST'])
def toggle_time():
    """暂停/继续时间"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        # 切换时间运行状态
        hotel.time_running = not hotel.time_running
        db.session.commit()

        status_text = "继续运行" if hotel.time_running else "已暂停"
        return jsonify({
            "success": True,
            "message": f"时间{status_text}",
            "time_running": hotel.time_running
        })
    except Exception as e:
        logger.error(f"切换时间状态时出错: {e}")
        return jsonify({"success": False, "message": "切换时间状态失败"}), 500


@bp.route('/api/get_save_slots', methods=['GET'])
def get_save_slots():
    """获取存档槽信息"""
    try:
        slots = {}
        for i in range(1, 4):  # 3个存档槽
            save_file = f'saves/slot_{i}.json'
            if os.path.exists(save_file):
                with open(save_file, 'r', encoding='utf-8') as f:
                    save_data = json.load(f)
                    slots[f'slot{i}'] = {
                        'hotel_name': save_data.get('hotel_name', '未知酒店'),
                        'date': save_data.get('date', '1990-01-01'),
                        'level': save_data.get('level', 1),
                        'money': save_data.get('money', 0),
                        'days_elapsed': save_data.get('days_elapsed', 0)
                    }
            else:
                slots[f'slot{i}'] = None

        return jsonify({"success": True, "slots": slots})
    except Exception as e:
        logger.error(f"获取存档槽信息时出错: {e}")
        return jsonify({"success": False, "message": "获取存档信息失败"}), 500


@bp.route('/api/save_to_slot', methods=['POST'])
def save_to_slot():
    """保存到指定存档槽"""
    try:
        data = request.get_json()
        slot_number = data.get('slot')

        if not slot_number or slot_number not in [1, 2, 3]:
            return jsonify({"success": False, "message": "无效的存档槽"}), 400

        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        # 创建存档目录
        os.makedirs('saves', exist_ok=True)

        # 收集所有游戏数据
        save_data = {
            'hotel_name': hotel.name,
            'hotel_level': hotel.level,
            'date': hotel.date.strftime('%Y-%m-%d'),
            'money': hotel.money,
            'days_elapsed': hotel.days_elapsed,
            'satisfaction': hotel.satisfaction,
            'reputation': hotel.reputation,
            'reputation_level': hotel.reputation_level,
            'time_speed': hotel.time_speed,
            'time_running': hotel.time_running,
            'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),

            # 员工数据
            'employees': [
                {
                    'name': emp.name,
                    'department': emp.department,
                    'level': emp.level,
                    'salary': emp.salary,
                    'hire_date': emp.hire_date.strftime('%Y-%m-%d') if emp.hire_date else None
                }
                for emp in Employee.query.filter_by(hotel_id=hotel.id).all()
            ],

            # 部门数据
            'departments': [
                {
                    'name': dept.name,
                    'is_unlocked': dept.is_unlocked,
                    'unlock_cost': dept.unlock_cost
                }
                for dept in Department.query.filter_by(hotel_id=hotel.id).all()
            ],

            # 房间数据
            'rooms': [
                {
                    'type': room.type,
                    'count': room.count,
                    'price': room.price
                }
                for room in Room.query.filter_by(hotel_id=hotel.id).all()
            ]
        }

        # 保存到文件
        save_file = f'saves/slot_{slot_number}.json'
        with open(save_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        return jsonify({
            "success": True,
            "message": f"游戏已保存到存档槽 {slot_number}"
        })
    except Exception as e:
        logger.error(f"保存到存档槽时出错: {e}")
        return jsonify({"success": False, "message": "保存失败"}), 500


@bp.route('/api/load_from_slot', methods=['POST'])
def load_from_slot():
    """从指定存档槽读取"""
    try:
        data = request.get_json()
        slot_number = data.get('slot')

        if not slot_number or slot_number not in [1, 2, 3]:
            return jsonify({"success": False, "message": "无效的存档槽"}), 400

        save_file = f'saves/slot_{slot_number}.json'
        if not os.path.exists(save_file):
            return jsonify({"success": False, "message": "存档不存在"}), 404

        # 读取存档数据
        with open(save_file, 'r', encoding='utf-8') as f:
            save_data = json.load(f)

        # 清除现有数据
        Hotel.query.delete()
        Employee.query.delete()
        Department.query.delete()
        Room.query.delete()
        FinancialRecord.query.delete()

        # 恢复酒店数据
        hotel = Hotel(
            name=save_data['hotel_name'],
            level=save_data['hotel_level'],
            date=datetime.strptime(save_data['date'], '%Y-%m-%d').date(),
            money=save_data['money'],
            days_elapsed=save_data['days_elapsed'],
            satisfaction=save_data['satisfaction'],
            reputation=save_data['reputation'],
            reputation_level=save_data['reputation_level'],
            time_speed=save_data['time_speed'],
            time_running=save_data['time_running']
        )
        db.session.add(hotel)
        db.session.flush()  # 获取hotel.id

        # 恢复部门数据
        for dept_data in save_data['departments']:
            department = Department(
                hotel_id=hotel.id,
                name=dept_data['name'],
                is_unlocked=dept_data['is_unlocked'],
                unlock_cost=dept_data['unlock_cost']
            )
            db.session.add(department)

        # 恢复员工数据
        for emp_data in save_data['employees']:
            employee = Employee(
                hotel_id=hotel.id,
                name=emp_data['name'],
                department=emp_data['department'],
                level=emp_data['level'],
                salary=emp_data['salary'],
                hire_date=datetime.strptime(emp_data['hire_date'], '%Y-%m-%d').date() if emp_data['hire_date'] else None
            )
            db.session.add(employee)

        # 恢复房间数据
        for room_data in save_data['rooms']:
            room = Room(
                hotel_id=hotel.id,
                type=room_data['type'],
                count=room_data['count'],
                price=room_data['price']
            )
            db.session.add(room)

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"存档槽 {slot_number} 读取成功"
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"读取存档槽时出错: {e}")
        return jsonify({"success": False, "message": "读取存档失败"}), 500


@bp.route('/api/restart_game', methods=['POST'])
def restart_game():
    """重新开始游戏"""
    try:
        # 停止时间线程
        from app.main.utils import stop_time_thread, start_time_thread
        stop_time_thread()

        # 重新初始化游戏数据
        from app.models import initialize_game_data, Achievement, MarketingCampaign, RandomEvent

        # 删除现有数据
        Hotel.query.delete()
        Employee.query.delete()
        Department.query.delete()
        Room.query.delete()
        FinancialRecord.query.delete()
        Achievement.query.delete()
        MarketingCampaign.query.delete()
        RandomEvent.query.delete()

        # 重新初始化
        initialize_game_data()
        db.session.commit()

        # 重新启动时间线程
        start_time_thread()

        return jsonify({
            "success": True,
            "message": "游戏已重新开始，时间系统已重启"
        })
    except Exception as e:
        logger.error(f"重新开始游戏时出错: {e}")
        db.session.rollback()
        return jsonify({"success": False, "message": "重新开始游戏失败"}), 500


@bp.route('/api/update_hotel_name', methods=['POST'])
def update_hotel_name():
    """更新酒店名称"""
    try:
        data = request.get_json()
        new_name = data.get('name', '').strip()

        if not new_name:
            return jsonify({"success": False, "message": "酒店名称不能为空"}), 400

        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        hotel.name = new_name
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "酒店名称已更新",
            "new_name": new_name
        })
    except Exception as e:
        logger.error(f"更新酒店名称时出错: {e}")
        db.session.rollback()
        return jsonify({"success": False, "message": "更新酒店名称失败"}), 500


@bp.route('/api/advance_time', methods=['POST'])
def advance_time():
    """手动推进一天"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        # 推进一天
        from app.main.utils import time_advance
        time_advance(hotel)

        return jsonify({
            "success": True,
            "message": f"时间已推进到 {hotel.date.strftime('%Y-%m-%d')}"
        })
    except Exception as e:
        logger.error(f"推进时间时出错: {e}")
        return jsonify({"success": False, "message": "推进时间失败"}), 500


@bp.route('/api/occupancy_data', methods=['GET'])
def get_occupancy_data():
    """获取真实入住率数据"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        # 获取或创建真实的入住率数据
        occupancy_data = get_real_occupancy_data(hotel)
        return jsonify(occupancy_data)
    except Exception as e:
        logger.error(f"获取入住率数据时出错: {e}")
        return jsonify({"success": False, "message": "获取入住率数据失败"}), 500


def get_real_occupancy_data(hotel):
    """获取真实的入住率数据，基于酒店的实际运营情况"""
    try:
        from app.models import OccupancyRecord

        # 使用no_autoflush避免查询时的自动flush导致唯一约束冲突
        with db.session.no_autoflush:
            current_date = hotel.date
            start_date = current_date.replace(day=1)  # 月初

            # 获取酒店的所有房间类型
            rooms = Room.query.filter_by(hotel_id=hotel.id).all()
            room_types = [room.type for room in rooms]

            occupancy_data = {}

            for room_type in room_types:
                occupancy_data[room_type] = []

                # 获取该房间类型的总数量
                room_count = Room.query.filter_by(hotel_id=hotel.id, type=room_type).count()
                if room_count == 0:
                    continue

                # 初始化整个月的数据（30天）
                days_in_month = 30  # 固定30天显示

                for day_offset in range(days_in_month):
                    current_day = start_date + timedelta(days=day_offset)

                    # 如果日期超过当前日期，设置入住率为0
                    if current_day > current_date:
                        occupancy_data[room_type].append({
                            'date': current_day.strftime('%m-%d'),
                            'rate': 0.0
                        })
                        continue
                    # 查找该日期的入住率记录
                    record = OccupancyRecord.query.filter_by(
                        hotel_id=hotel.id,
                        room_type=room_type,
                        date=current_day
                    ).first()

                    if record:
                        # 使用真实记录的入住率
                        occupancy_rate = record.occupancy_rate
                    else:
                        # 如果没有记录，基于酒店状态计算入住率
                        occupancy_rate = calculate_daily_occupancy_rate(hotel, room_type, current_day)

                        # 创建或更新入住率记录（安全upsert模式）
                        try:
                            # 使用merge操作避免唯一约束冲突
                            existing_record = OccupancyRecord.query.filter_by(
                                hotel_id=hotel.id,
                                room_type=room_type,
                                date=current_day
                            ).first()

                            if existing_record:
                                # 如果记录已存在，更新入住率
                                existing_record.occupancy_rate = occupancy_rate
                            else:
                                # 如果记录不存在，创建新记录
                                new_record = OccupancyRecord(
                                    hotel_id=hotel.id,
                                    room_type=room_type,
                                    date=current_day,
                                    occupancy_rate=occupancy_rate
                                )
                                # 使用merge避免重复插入
                                db.session.merge(new_record)
                        except Exception as e:
                            logger.warning(f"处理入住率记录时出错: {e}")
                            # 如果处理失败，继续使用计算的入住率

                    occupancy_data[room_type].append({
                        'date': (start_date + timedelta(days=day_offset)).strftime('%m-%d'),
                        'rate': round(occupancy_rate, 1)
                    })

            # 安全提交数据库更改
            try:
                db.session.commit()
            except Exception as commit_error:
                logger.warning(f"提交入住率记录时出错: {commit_error}")
                db.session.rollback()
                # 即使提交失败，也返回计算的数据

            return occupancy_data

    except Exception as e:
        logger.error(f"获取真实入住率数据时出错: {e}")
        db.session.rollback()
        return {}


def calculate_daily_occupancy_rate(hotel, room_type, date):
    """计算特定日期的真实入住率"""
    try:
        # 基础入住率（基于酒店等级和声望）
        base_rate = min(0.9, 0.3 + (hotel.level * 0.1) + (hotel.reputation / 1000))

        # 客户满意度影响
        satisfaction_factor = hotel.satisfaction / 100

        # 房间类型影响
        room_type_factors = {
            '单人间': 0.8,
            '标准间': 1.0,
            '豪华间': 0.7,
            '套房': 0.5
        }
        room_factor = room_type_factors.get(room_type, 1.0)

        # 周末加成
        weekend_factor = 1.2 if date.weekday() >= 5 else 1.0

        # 月份季节性影响
        month_factors = {
            1: 0.7, 2: 0.8, 3: 0.9, 4: 1.0, 5: 1.1, 6: 1.2,
            7: 1.3, 8: 1.2, 9: 1.0, 10: 0.9, 11: 0.8, 12: 0.9
        }
        season_factor = month_factors.get(date.month, 1.0)

        # 计算最终入住率
        final_rate = base_rate * satisfaction_factor * room_factor * weekend_factor * season_factor

        # 添加小幅随机波动（±5%）
        import random
        random_factor = random.uniform(0.95, 1.05)
        final_rate *= random_factor

        # 确保在合理范围内
        return max(0.0, min(100.0, final_rate * 100))

    except Exception as e:
        logger.error(f"计算日入住率时出错: {e}")
        return 50.0  # 默认50%入住率