{% extends "base.html" %}

{% block title %}部门管理 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-diagram-3-fill text-primary me-2"></i>部门管理</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<!-- 部门概况 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-pie-chart text-primary me-2"></i>部门概况
                </h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-check-circle-fill text-success fs-3 mb-2"></i>
                            <h4 class="text-success mb-1">{{ all_departments|selectattr('is_unlocked')|list|length }}</h4>
                            <small class="text-muted">已解锁部门</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-people-fill text-primary fs-3 mb-2"></i>
                            <h4 class="text-primary mb-1">{{ department_employee_counts.values()|sum }}</h4>
                            <small class="text-muted">员工总数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-speedometer2 text-warning fs-3 mb-2"></i>
                            {% set unlocked_busy_levels = [] %}
                            {% for dept in all_departments %}
                                {% if dept.is_unlocked and department_busy_levels.get(dept.name, 0) > 0 %}
                                    {% set _ = unlocked_busy_levels.append(department_busy_levels[dept.name]) %}
                                {% endif %}
                            {% endfor %}
                            <h4 class="text-warning mb-1">{{ "%.1f"|format(unlocked_busy_levels|sum / unlocked_busy_levels|length if unlocked_busy_levels|length > 0 else 0) }}%</h4>
                            <small class="text-muted">平均繁忙度</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-lock-fill text-secondary fs-3 mb-2"></i>
                            <h4 class="text-secondary mb-1">{{ all_departments|rejectattr('is_unlocked')|list|length }}</h4>
                            <small class="text-muted">待解锁部门</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 部门列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-body">
        <h5 class="card-title mb-3">
            <i class="bi bi-diagram-3-fill text-primary me-2"></i>部门管理
        </h5>
        <div class="table-responsive">
            <table class="table table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th width="5%">状态</th>
                        <th width="15%">部门名称</th>
                        <th width="8%">总员工</th>
                        <th width="8%">初级</th>
                        <th width="8%">中级</th>
                        <th width="8%">高级</th>
                        <th width="8%">特级</th>
                        <th width="10%">繁忙度</th>
                        <th width="15%">解锁条件</th>
                        <th width="10%">解锁费用</th>
                        <th width="5%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for department in all_departments %}
                    <tr class="{% if department.is_unlocked %}table-success{% endif %}">
                        <td>
                            {% if department.is_unlocked %}
                            <i class="bi bi-check-circle-fill text-success fs-5"></i>
                            {% else %}
                            <i class="bi bi-lock-fill text-muted fs-5"></i>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <strong>{{ department.name }}</strong>
                                {% if department.is_unlocked %}
                                <span class="badge bg-success ms-2">已解锁</span>
                                {% else %}
                                <span class="badge bg-secondary ms-2">未解锁</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="badge bg-primary">{{ department_employee_counts.get(department.name, 0) }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="badge bg-secondary">{{ department_level_counts.get(department.name, {}).get('初级', 0) }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="badge bg-info">{{ department_level_counts.get(department.name, {}).get('中级', 0) }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="badge bg-success">{{ department_level_counts.get(department.name, {}).get('高级', 0) }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="badge bg-warning">{{ department_level_counts.get(department.name, {}).get('特级', 0) }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <div class="d-flex align-items-center">
                                <div class="progress me-2" style="width: 80px; height: 8px;">
                                    <div class="progress-bar bg-info" style="width: {{ department_busy_levels.get(department.name, 0) }}%"></div>
                                </div>
                                <small>{{ "%.1f"|format(department_busy_levels.get(department.name, 0)) }}%</small>
                            </div>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if not department.is_unlocked %}
                            <small class="text-muted">需要{{ department.required_level }}星酒店</small>
                            {% else %}
                            <span class="text-success">✓ 已满足</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if not department.is_unlocked %}
                            <span class="text-warning">¥{{ "{:,}".format(department.unlock_cost) }}</span>
                            {% else %}
                            <span class="text-success">已支付</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="text-muted">--</span>
                            {% elif department.can_unlock %}
                            <button class="btn btn-sm btn-warning" onclick="unlockDepartment({{ department.id }})">
                                <i class="bi bi-unlock-fill"></i>
                            </button>
                            {% else %}
                            <span class="text-muted">--</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function unlockDepartment(departmentId) {
    if (confirm('确定要解锁这个部门吗？')) {
        apiRequest(`/departments/unlock/${departmentId}`, {
            method: 'POST'
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('解锁部门失败', 'error');
        });
    }
}
</script>
{% endblock %}
