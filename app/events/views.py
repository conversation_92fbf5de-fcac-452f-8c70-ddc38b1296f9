#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
事件系统视图
"""

from flask import Blueprint, jsonify, request, render_template
from app.models import Hotel, FinancialRecord, db
from app.main.utils import get_current_hotel
import random
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

bp = Blueprint('events', __name__, url_prefix='/events')

# 随机事件配置
RANDOM_EVENTS = [
    {
        'id': 'power_outage',
        'name': '停电事故',
        'description': '酒店突然停电，需要紧急维修',
        'type': 'negative',
        'money_effect': -50000,
        'reputation_effect': -10,
        'probability': 0.05
    },
    {
        'id': 'celebrity_visit',
        'name': '名人入住',
        'description': '知名人士入住酒店，提升了酒店声誉',
        'type': 'positive',
        'money_effect': 100000,
        'reputation_effect': 50,
        'probability': 0.03
    },
    {
        'id': 'food_poisoning',
        'name': '食物中毒',
        'description': '餐厅发生食物中毒事件，需要赔偿和整改',
        'type': 'negative',
        'money_effect': -80000,
        'reputation_effect': -20,
        'probability': 0.02
    },
    {
        'id': 'good_review',
        'name': '好评如潮',
        'description': '酒店获得大量好评，吸引更多客户',
        'type': 'positive',
        'money_effect': 30000,
        'reputation_effect': 15,
        'probability': 0.08
    },
    {
        'id': 'equipment_failure',
        'name': '设备故障',
        'description': '酒店设备出现故障，需要维修费用',
        'type': 'negative',
        'money_effect': -25000,
        'reputation_effect': -5,
        'probability': 0.06
    },
    {
        'id': 'tourism_boom',
        'name': '旅游旺季',
        'description': '当地旅游业兴旺，酒店收入大增',
        'type': 'positive',
        'money_effect': 150000,
        'reputation_effect': 20,
        'probability': 0.04
    },
    {
        'id': 'staff_strike',
        'name': '员工罢工',
        'description': '部分员工要求加薪，需要额外支出',
        'type': 'negative',
        'money_effect': -40000,
        'reputation_effect': -8,
        'probability': 0.03
    },
    {
        'id': 'award_winning',
        'name': '获得奖项',
        'description': '酒店获得行业奖项，声誉大增',
        'type': 'positive',
        'money_effect': 80000,
        'reputation_effect': 40,
        'probability': 0.02
    }
]

# 月底事件配置
MONTHLY_EVENTS = [
    {
        'id': 'monthly_report',
        'name': '月度总结',
        'description': '查看本月经营情况',
        'type': 'neutral',
        'show_financial_summary': True
    },
    {
        'id': 'tax_payment',
        'name': '税务缴纳',
        'description': '需要缴纳本月税款',
        'type': 'negative',
        'money_effect_percent': 0.02  # 收入的2%
    },
    {
        'id': 'bonus_distribution',
        'name': '员工奖金',
        'description': '发放员工月度奖金',
        'type': 'neutral',
        'money_effect_per_employee': 1000
    }
]

@bp.route('/check_random_event')
def check_random_event():
    """检查是否触发随机事件"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"})
        
        # 随机检查是否触发事件
        for event in RANDOM_EVENTS:
            if random.random() < event['probability']:
                return jsonify({
                    "success": True,
                    "has_event": True,
                    "event": event
                })
        
        return jsonify({
            "success": True,
            "has_event": False
        })
        
    except Exception as e:
        logger.error(f"检查随机事件时出错: {e}")
        return jsonify({"success": False, "message": "检查随机事件失败"})

@bp.route('/check_monthly_event')
def check_monthly_event():
    """检查月底事件"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"})
        
        # 检查是否是月底（每月28-31日）
        current_day = hotel.date.day
        if current_day >= 28:
            # 随机选择一个月底事件
            event = random.choice(MONTHLY_EVENTS)
            
            # 计算事件效果
            if event['id'] == 'tax_payment':
                # 计算税款（基于酒店资金的2%）
                tax_amount = int(hotel.money * event.get('money_effect_percent', 0.02))
                event['money_effect'] = -tax_amount
                event['description'] = f"需要缴纳税款：¥{tax_amount:,}"
            elif event['id'] == 'bonus_distribution':
                # 计算员工奖金
                from app.models import Employee
                employee_count = Employee.query.filter_by(hotel_id=hotel.id).count()
                bonus_total = employee_count * event.get('money_effect_per_employee', 1000)
                event['money_effect'] = -bonus_total
                event['description'] = f"发放{employee_count}名员工奖金：¥{bonus_total:,}"
            
            return jsonify({
                "success": True,
                "has_event": True,
                "event": event
            })
        
        return jsonify({
            "success": True,
            "has_event": False
        })
        
    except Exception as e:
        logger.error(f"检查月底事件时出错: {e}")
        return jsonify({"success": False, "message": "检查月底事件失败"})

@bp.route('/handle_event', methods=['POST'])
def handle_event():
    """处理事件"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"})
        
        data = request.get_json()
        event_id = data.get('event_id')
        action = data.get('action', 'accept')  # accept 或 dismiss
        
        if action == 'dismiss':
            return jsonify({"success": True, "message": "事件已忽略"})
        
        # 查找事件
        event = None
        for e in RANDOM_EVENTS + MONTHLY_EVENTS:
            if e['id'] == event_id:
                event = e
                break
        
        if not event:
            return jsonify({"success": False, "message": "事件不存在"})
        
        # 应用事件效果
        money_effect = event.get('money_effect', 0)
        reputation_effect = event.get('reputation_effect', 0)
        
        if money_effect != 0:
            hotel.money += money_effect
            
            # 记录财务记录
            financial_record = FinancialRecord(
                hotel_id=hotel.id,
                record_date=hotel.date,
                description=f"随机事件：{event['name']}",
                income=max(0, money_effect),
                expense=max(0, -money_effect)
            )
            db.session.add(financial_record)
        
        if reputation_effect != 0:
            hotel.reputation += reputation_effect
        
        db.session.commit()
        
        effect_text = []
        if money_effect > 0:
            effect_text.append(f"获得¥{money_effect:,}")
        elif money_effect < 0:
            effect_text.append(f"损失¥{-money_effect:,}")
        
        if reputation_effect > 0:
            effect_text.append(f"声望+{reputation_effect}")
        elif reputation_effect < 0:
            effect_text.append(f"声望{reputation_effect}")
        
        message = f"事件处理完成"
        if effect_text:
            message += f"：{', '.join(effect_text)}"
        
        return jsonify({
            "success": True,
            "message": message
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"处理事件时出错: {e}")
        return jsonify({"success": False, "message": "处理事件失败"})
